.gever-gridRowTextBtn>a {display: inline-block;width: auto;height: auto;margin: 0 5px;background: transparent;color: #109aff;}
.gever-financial-report {width:100%; border-top:1px solid #e1e1e1; border-left:1px solid #e1e1e1; margin:5px 0px 10px 0px;}
.gever-financial-report thead th,
.gever-financial-report tbody td {border-color: #e1e1e1; border-style: solid; border-width: 0 1px 1px 0; text-align:center; line-height:28px;}
.gever-financial-report thead th {border-bottom:1px solid #a7cdf1; background-color:#edf7ff; text-align:center; color:#3369ae; line-height:38px;}
.gever-financial-report-datagrid-cell {overflow: hidden; text-overflow: ellipsis; font-size: 14px; padding: 0px; margin: 0px 4px; color: #384047; word-break: break-all;}
.gever-financial-report-headWrap {overflow: hidden; text-overflow:ellipsis; white-space: nowrap; width: 220px;}
.gever-financial-divTextarea{border:1px solid #D3D3D3; background-color: #f3f3f3; padding:4px; border-radius: 5px; color: #5c5c5c; font-size: 14px; line-height: 20px; height:100px;}
.gever-financial-checkResultBg{border:1px solid #D3D3D3; background-color: #f3f3f3; border-radius: 5px; color: #333333; -moz-border-radius: 5px; -webkit-border-radius: 5px; width: 99%;}
.gever-financial-checkResultBg > div{height:100px;overflow-y: auto;padding: 5px 5px; font-size: 14px; line-height: 20px;}
.gever-formContentGroup {height: 100%; padding: 5px 0; border-bottom: 1px solid #ccc;}
.gever-voucherGroup {padding-top: 10px; position: relative}
.gever-groupHeader {height: 35px; padding-bottom: 5px;}
.gever-groupHeader-Mul {height: 60px; padding-bottom: 20px;}
.gever-groupHeaderContent, .gever-groupContent {position: relative; padding: 0 20px;}
.gever-groupContent {margin-bottom: 20px;}
.gever-groupContent .gever-groupTbBox {width: 99%;}
.gever-formTable .textbox {border: 0;border-bottom: 1px solid #d3d3d3;box-shadow: none;border-radius: 0;margin-right: 5px;}
.combobox-row-btn {background: transparent;text-decoration:underline;width: 35px;color: #109aff}
.combobox-item-selected .combobox-row-btn {color: #FFFFFF}
.combobox-row-text {word-break:break-all;}