span {
  font-size: 12px;
  line-height: 14px;
}
.floatRight {
    float: right;
}
.printVoucherTb,.printVoucherTb tr,.printVoucherTb tr td{
    border: 1px solid #000;
    border-collapse: collapse;
}
.printVoucherTb{
    table-layout:fixed;
}
.printVoucherTb tr{
    font-size: 12px;
}
.printVoucherTb tr td {
    height:30px;
    padding: 0px 3px;
}
.printMain {
    width: 700px;
    height: 900px;
    margin: 10px auto;
    position: relative;
}
.printContent {
    width: 100%;
    padding: 10px 0;
    display: inline-block;
    border-collapse:collapse;
    margin-top: 18px
}
.printContent .title {
    font-weight: 500;
    font-size: 22px;
    text-align: center;
    width: 20%;
    letter-spacing: 15px;
    margin: 0 auto;
    position: relative;
    /*border-bottom:2pt double #000000*/
}
.printContent .line {
    padding: 0;
    width: 150px;
    height: 1px;
    border-top: 1px solid #000;
    border-bottom: 1px solid #000;
    margin: 0 auto;
    box-sizing: border-box;
}
.printContent .explain {
    margin: 0 2px 0;
}
.printContent .explain span {
    display: inline-block;
}
.printContent .explain .name {
    width: 45%;
}
.printContent .explain .date {
    width: 10%;
    text-align: center;
}
.printContent .explain .number {
    width: 45%;
    text-align: right;
}
.printContent .people, .printContent .num {
    margin: 7px 2px 0px 2px;
}
.floatRight > span {
    margin-left: 20px;
}
