@charset "utf-8";

/* CSS Document */
body {
  padding: 0;
  margin: 0;
}

#app {
  /* min-width: 1560px; */
}

#carousel {
  position: relative;
  width: 1400px;
  margin: 0 auto;
}

@media screen and (max-width: 1680px) {
  #carousel .swiper-button-prev {
    left: -80px !important;
  }

  #carousel .swiper-button-next {
    right: -80px !important;
  }
}

#carousel .swiper {
  padding-top: 20px;
  padding-bottom: 40px;
}

#carousel > .swiper > .swiper-wrapper {
  /* transform: translate3d(-1390px, 0px, 0px) !important; */
}

#carousel .swiper-slide {
  width: 200px;
  height: 300px;
  /* background: #fff; */
  /* box-shadow: 0 8px 20px #ddd; */
}

#carousel .swiper-slide img {
  display: block;
  width: 100%;
}

#carousel .swiper-slide p {
  line-height: 58px;
  padding-top: 0;
  text-align: center;
  color: #636363;
  font-size: 0.8em;
  margin: 0;
}

#carousel .swiper-pagination {
  width: 100%;
  bottom: 20px;
}

#carousel .swiper-pagination-bullets .swiper-pagination-bullet {
  margin: 0 5px;
  background-color: #e8f5ff;
  width: 20px;
  height: 10px;
  opacity: 1;
  border-radius: 5px;
}

#carousel .swiper-pagination-bullets .swiper-pagination-bullet-active {
  background-color: #3eb2f3;
  width: 35px;
}

#carousel .swiper-button-prev {
  /* left: -50px;
	width: 45px;
	height: 45px;
	background: url(../images/wm_button_icon.png) no-repeat;
	background-position: 0 0;
	background-size: 100%; */
  position: absolute;
  background-image: url(../../assets/images/portal/btn_page_left_old.png);
  height: 152px;
  width: 80px;
  background-size: contain;
  left: -150px;
  outline: none;
  top: 40%;
  z-index: 99;
  cursor: pointer;
}

#carousel .swiper-button-prev:hover {
  /* background-position: 0 -46px; */
  /* background-size: 100% */
  background-image: url(../../assets/images/portal/btn_page_left.png);
}

#carousel .swiper-button-next {
  /* right: -50px;
	width: 45px;
	height: 45px;
	background: url(../images/wm_button_icon.png) no-repeat;
	background-position: 0 -93px;
	background-size: 100%; */
  position: absolute;
  background-image: url(../../assets/images/portal/btn_page_right_old.png);
  height: 152px;
  width: 80px;
  background-size: contain;
  right: -150px;
  outline: none;
  top: 40%;
  z-index: 99;
  cursor: pointer;
}

#carousel .swiper-button-next:hover {
  /* background-position: 0 -139px;
	background-size: 100% */
  background-image: url(../../assets/images/portal/btn_page_right.png);
}

#carousel .swiper-button-prev::after,
#carousel .swiper-button-next::after {
  content: '';
}
