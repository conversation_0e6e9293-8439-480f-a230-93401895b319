body{
    margin:0px;
    padding:0;
}
span {
    font-size: 12px;
}
.floatRight {
    float: right;
}
.printVoucherTb,.printVoucherTb tr,.printVoucherTb tr td{
    border: 1px solid #000;
    border-collapse: collapse;
}
.printVoucherTb{
    table-layout:fixed;
}
.printVoucherTb tr{
    font-size: 12px;
}
.printVoucherTb tr td {
    height:20px;
    padding: 0px 3px;
}
/*.printVoucherTb .tbBody td {*/
/*padding: 5px 0;*/
/*}*/
/*.printVoucherTb .tbTitle td {*/
/*line-height: 6mm;*/
/*}*/
/*.printVoucherTb .tbBodySum td{*/
/*padding: 9px 0;*/
/*}*/

.printContent {
    padding: 0px 0px;
    display: inline-block;
    margin: 0 0;
}
.printContent .title {
    width: 100%;
    font-weight: 500;
    font-size: 22px;
    text-align: center;
    letter-spacing: 15px;
    margin: 0 0;
    position: relative;
}
.printContent .line {
    width: 150px;
    height: 1mm;
    line-height: 0;
    letter-spacing: 0;
    border-top: 1px solid #000;
    border-bottom: 1px solid #000;
    margin: 0 auto;
    padding: 0;
}
.printContent .explain {
    margin: 0 0mm;
}
.printContent .explain span {
    display: inline-block;
}
.printContent .explain .name {
    width: 45%;
}
.printContent .explain .date {
    width: 10%;
    text-align: center;
}
.printContent .explain .number {
    width: 44%;
    text-align: right;
}
.printContent .num {
    margin: 0px 2px 0px 2px;
}
.printContent .people {
    line-height: 1mm;
    margin: 0px 0px 0px 0px;
}
.floatRight > span {

}