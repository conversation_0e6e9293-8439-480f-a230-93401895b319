<template>
  <div v-if="isFullScreenComponent" class="app-wrapper">
    <router-view />
    <micro-app
      v-if="microApp.name"
      style="width: 100%; height: 100%"
      :name="microApp.name"
      :url="microApp.url"
      :baseroute="microApp.baseroute"
      @datachange="microApp.microAppChange"
    />
  </div>
  <div
    v-else
    :class="classObj"
    class="app-wrapper"
    :style="{ '--current-color': theme }"
  >
    <desk-head />
    <router-view
      v-if="isFullScreenComponentWithHeader"
      style="margin-top: 60px"
    />
    <div v-else>
      <div
        v-if="device === 'mobile' && sidebar.opened"
        class="drawer-bg"
        @click="handleClickOutside"
      />
      <template v-if="!isFullScreenComponentWithHeader_micro">
        <sidebar
          class="sidebar-container"
          :style="{
            backgroundColor:
              sideTheme === 'theme-dark'
                ? 'menuBackground'
                : 'menuLightBackground'
          }"
        />

        <div :class="{ hasTagsView: needTagsView }" class="main-container">
          <div :class="{ 'fixed-header': fixedHeader }">
            <tags-view v-if="needTagsView" ref="_tagsViewRef" />
          </div>
          <micro-app
            v-if="microApp.name"
            :name="microApp.name"
            :url="microApp.url"
            :baseroute="microApp.baseroute"
            @datachange="microApp.microAppChange"
          />
          <app-main v-else />
        </div>
      </template>
      <template v-else>
        <micro-app
          v-if="microApp.name"
          :name="microApp.name"
          :url="microApp.url"
          :baseroute="microApp.baseroute"
          @datachange="microApp.microAppChange"
        />
      </template>
    </div>
  </div>
</template>

<script>
import { AppMain, Sidebar, TagsView } from './components'
import DeskHead from './components/DeskHead/index.vue'
import ResizeMixin from './mixin/ResizeHandler'
import { mapState } from 'vuex'
// import variables from '@base/src/assets/styles/variables.scss'
import microApp from '@micro-zoe/micro-app'
export default {
  name: 'Layout',
  components: {
    AppMain,
    Sidebar,
    TagsView,
    DeskHead
  },
  mixins: [ResizeMixin],
  data() {
    return {
      microApp: {}
    }
  },
  computed: {
    ...mapState({
      theme: (state) => state.settings.theme,
      sideTheme: (state) => state.settings.sideTheme,
      sidebar: (state) => state.app.sidebar,
      device: (state) => state.app.device,
      needTagsView: (state) => state.settings.tagsView,
      fixedHeader: (state) => state.settings.fixedHeader
    }),
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === 'mobile'
      }
    },
    // variables() {
    //   return variables
    // },
    isFullScreenComponent() {
      const fullScreenComponents = [
        '/financial/accounting/help',
        '/leaderView/leaderSence',
        '/leaderView/leaderSence/cash',
        '/leaderView/leaderSence/asset',
        '/leaderView/leaderSence/poverty'
      ]
      return fullScreenComponents.indexOf(this.$route.path) > -1
    },
    isFullScreenComponentWithHeader() {
      // 消息盒
      const components = ['/message/messageCenter', '/work/workDesk']
      return components.indexOf(this.$route.path) > -1
    },
    isFullScreenComponentWithHeader_micro() {
      const components = ['/monitoring/comprehensive']
      return components.indexOf(this.$route.path) > -1
    }
  },
  watch: {
    $route: {
      handler(to, from) {
        if (process.env.NODE_ENV === 'development') {
          this.conf = window.conf_dev
        } else {
          this.conf = window.conf_prod
        }
        if (to.matched[0].name === from.matched[0].name && this.microApp.name) {
          return
        }
        // microApp.setData(from.matched[0].name, { type: 'delAllViews' })
        const app = this.conf.microAppList.filter((item) => {
          if (item.name == to.matched[0].name) {
            return item
          }
        })
        this.microApp = { ...app[0] }
        this.microApp.microAppChange =
          this[this.microApp.microAppChange || 'handleMicroChange']
        microApp.start()
      }
    }
  },
  mounted() {
    if (process.env.NODE_ENV === 'development') {
      this.conf = window.conf_dev
    } else {
      this.conf = window.conf_prod
    }
    const app = this.conf.microAppList.filter((item) => {
      if (item.name == this.$route.matched[0].name) {
        return item
      }
    })
    this.microApp = { ...app[0] }
    this.microApp.microAppChange =
      this[this.microApp.microAppChange || 'handleMicroChange']
    // microApp.setData(this.microApp.name, { type: 'openMenu', path: this.$route.path })
    // 预加载
    const preFetchApps = []
    for (let i = 0; i < this.conf.microAppList.length; i++) {
      const ele = this.conf.microAppList[i]
      if (ele.name != this.microApp.name) {
        const obj = {
          name: ele.name,
          url: ele.url
        }
        preFetchApps.push(obj)
      }
    }
    microApp.start({
      preFetchApps
    })
  },
  methods: {
    handleClickOutside() {
      this.$store.dispatch('app/closeSideBar', { withoutAnimation: false })
    },
    handleMicroChange(data) {
      if (data.detail.data.type === 'openMenu') {
        if (data.detail.data.path === '/login') {
          this.$confirm(
            this.$t('登录超时，请重新登录！'),
            this.$t('系统提示'),
            {
              confirmButtonText: this.$t('确定'),
              showClose: false,
              showCancelButton: false,
              closeOnClickModal: false,
              closeOnPressEscape: false,
              type: 'warning'
            }
          )
            .then(() => {
              this.$store.dispatch('LogOut').then(() => {
                if (window.__MICRO_APP_ENVIRONMENT__) {
                  window.microApp.dispatch({
                    type: 'openMenu',
                    path: '/login'
                  })
                } else {
                  this.$router.go(0)
                  this.$store.commit('SET_TOKENFLAG', false)
                }
              })
            })
            .catch(() => {})
          return
        }
        console.log('handleMicroChange', data)
        this.$router.push({
          path: data.detail.data.path,
          query: data.detail.data.query || {}
        })
      }
    },
    handleFinancialChange(data) {
      if (data.detail.data.type === 'openMenu') {
        this.$router.push({
          path: data.detail.data.path,
          query: data.detail.data.query || {}
        })
        const path = data.detail.data.path
        microApp.setData('Financial', {
          type: 'openMenu',
          path: path
        })
        // this.$store.dispatch('tagsView/addView', {
        //   path: data.detail.data.path,
        //   na
        // })
      } else if (data.detail.data.type === 'delOthersViews') {
        this.$store.dispatch('tagsView/delOthersViews', data.detail.data.data)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@base/src/assets/styles/core/mixin.scss';
// @import '~@base/src/assets/styles/variables.scss';

.app-wrapper {
  @include clearfix;
  position: relative;
  height: 100%;
  width: 100%;
  overflow: hidden;
  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}

.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
  width: calc(100% - #{$base-sidebar-width});
  transition: width 0.28s;
}

.hideSidebar .fixed-header {
  width: calc(100% - 54px);
}

.mobile .fixed-header {
  width: 100%;
}
</style>
