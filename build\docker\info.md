# 一、Docker编译及容器启动
## 1.Dockerfile
### 1.1 Docker编译命令
```
docker build -t 镜像仓库/项目名/镜像名:镜像版本号 .
#例如：docker build -t ************/szxc-dev/monolith-ui:20220519-100941 .
```

### 1.2 Dockerfile解析

```
# a.基础镜像
FROM nginx

# b.指定镜像作者信息
LABEL MAINTAINER="gever"

# c.将npm编译后的静态文件拷贝 到 nginx镜像中的静态资源目录
COPY rural-ui/  /usr/share/nginx/html/

# d.拷贝nginx.conf配置文件 到 nginx镜像中的配置目录
COPY nginx.conf /etc/nginx/nginx.conf

# e.拷贝nginx.conf2配置文件 到 nginx镜像中的配置目录
# 注：nginx.conf2与nginx.conf的不同之处在于增加了XXL_JOB_URL配置（定时器配置）
COPY nginx.conf2 /etc/nginx/nginx.conf2

# f.启动命令，使用扩展的startup.sh脚本进行启动
CMD ["bash", "/startup.sh"]
```

startup.sh脚本解析
```
#!/bin/bash
CONFIG_FILE=/etc/nginx/nginx.conf

if [[ -z $MONOLITH ]]; then
	  MONOLITH=0
fi
# 启用单体后端适配，修改对应的登录接口
if [[ $MONOLITH -eq 1 ]]; then
    CONFIG_FILE=/etc/nginx/nginx.conf2
fi
# 设置定时任务路径转发
if [[ $XXL_JOB_URL ]]; then
    sed -i -e "s#XXL_JOB_URL#${XXL_JOB_URL}#g" $CONFIG_FILE
fi
# 设置网关路径转发
if [[ $API_URL ]]; then
	  sed -i -e "s#API_URL#${API_URL}#g" $CONFIG_FILE
fi

# 启动nginx
nginx -c $CONFIG_FILE  -g "daemon off;"

```

## 2 启动命令

### 2.1 微服务后端

```
docker run -d -it -p 10001:80  --name=rural-ui \
-e API_URL=http://*************:8001 \ 
************/szxc-dev/monolith-ui:20220519-100941
```
其中：


`-p 10001:80`
指定容器暴露端口，其中`10001`为宿主机端口，`80`为容器内部端口

`—name rural-ui`	
指定容器名称为rural-ui 

`-e API_URL=http://************:10083`	
传递网关地址（http://网关gdp-gateway的IP:端口）

### 2.2 单体后端

```
docker run -d -it -p 10001:80  --name=rural-ui \
-e API_URL=http://*************:8080 \ 
-e XXL_JOB_URL=http://*************:8090 \
-e MONOLITH=1 \
************/szxc-dev/monolith-ui:20220519-100941
```
其中：
`-p 10001:80`
指定容器暴露端口，其中`10001`为宿主机端口，`80`为容器内部端口

`—name rural-ui`	
指定容器名称为rural-ui 

`-e API_URL=http://************:8080`	
传递的后端项目地址（http://后端项目IP:端口）

`-e XXL_JOB_URL=http://*************:8090`	
传递的定时任务地址（http://定时任务IP:端口）

`-e MONOLITH=1`	
指定使用单体后端适配

# 二、nginx非容器启动
若不想使用docker容器启动，可以使用独立的nginx进行启动。
`注意：编译前需指定是否启用单体后端适配`

```
#在env文件中修改，true表示启动单体后端适配，false表示使用微服务后端适配
VUE_APP_IS_BACKEND_MONOLITH = true
```

## 1.将npm编译后的静态文件拷贝到nginx的静态资源目录
`rural-ui/` 是npm编译后的静态文件目录

`/usr/share/nginx/html/` nginx的静态资源目录

## 2.拷贝配置文件到nginx的配置目录
### 2.1拷贝配置文件

若是对接微服务后端，则拷贝`nginx.conf` 到nginx的/etc/nginx/nginx.conf

若是对接单体后端，则拷贝`nginx.conf2`到nginx的/etc/nginx/nginx.conf

### 2.2修改配置文件nginx.conf

```
    location /api/ {
        rewrite  /api/(.*)  /$1  break;
        proxy_pass API_URL; # 将API_URL修改为对应的后端访问地址
    }

    location /api/xxl-job-admin {
        rewrite  /api/(.*)  /$1  break;
        proxy_pass XXL_JOB_URL; #将XXL_JOB_URL修改为对应的定时任务访问地址
    }
```


## 3.nginx启动命令

```
nginx -c /etc/nginx/nginx.conf -g daemon off;
```