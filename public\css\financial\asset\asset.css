span {
  font-size: 15px;
  line-height: 14px;
}
.printVoucherTb,
.printVoucherTb tr,
.printVoucherTb tr td {
  border: 1px solid #000;
  border-collapse: collapse;
}
.printVoucherTb {
  table-layout: fixed;
}
.printVoucherTb tr {
  font-size: 12px;
}
.printVoucherTb tr td {
  height: 30px;
  padding: 0px 3px;
}
.printMain {
  width: 700px;
  height: 900px;
  margin: 10px auto;
  position: relative;
}
.printContent {
  width: 100%;
  padding: 10px 0;
  display: inline-block;
  margin-top: 20px;
}
.printContent .title {
  font-weight: 700;
  font-size: 25px;
  text-align: center;
  width: 100%;
  letter-spacing: 15px;
  margin: 0 auto;
  position: relative;
}
.printContent .line {
  width: 150px;
  height: 6px;
  line-height: 0;
  letter-spacing: 0;
  border-top: 1px solid #000;
  border-bottom: 1px solid #000;
  margin: 0 auto;
  padding: 0;
}
.printContent .explain {
  margin: 5px 2px 0;
}
.printContent .explain span {
  display: inline-block;
}
.printContent .explain .name {
  width: 45%;
}
.printContent .explain .date {
  width: 10%;
  text-align: center;
}
.printContent .explain .number {
  width: 45%;
  text-align: right;
}
.printContent table tr td {
  height: 40px;
  font-size: 15px;
}
.printFooter {
  margin-top: 10px;
}
.printFooter .spanval {
  margin: 7px 2px 0px 2px;
}
.printFooter .leftPart {
  float: left;
}
.printFooter .rightPart {
  float: right;
}
