/*
 * @Descripttion:
 * @version:
 * @Author: 未知
 * @Date: 2023-07-17 11:53:53
 * @LastEditors: Andy
 * @LastEditTime: 2023-09-06 15:53:06
 */
const hideBuildingAlert = true
const href = window.location.origin
const usePortal = 3 //  3=粤信签，1=用户名密码
const isWorkDesk = false // 是否启用工作台
const isShowMsgList = true // 首页是否启用消息列表
const baseRouter = ''
const yxqThirdCode = 'yxqcommon'
const showOtherLoginType = false // 是否显示其他登录方式
const countUserRefreshTime = 15 * 60000 // 单位毫秒  ， 前端统计后台在线人数接口刷新时间
const countUserSetTimeOut = 20 // 单位秒  ， 前端统计后台在线人数接口最大延迟执行时间
var conf_prod = {
  microAppList: [
    {
      name: 'System',
      url: href + baseRouter + '/zhxc-system/',
      baseroute: '/system'
    },
    {
      name: 'Assets',
      url: href + baseRouter + '/zhxc-assets/',
      baseroute: '/assets'
    },
    {
      name: 'Trading',
      url: href + baseRouter + '/zhxc-trading/',
      baseroute: '/trading'
    },
    {
      name: 'Contract',
      url: href + baseRouter + '/zhxc-contract/',
      baseroute: '/contract'
    },
    {
      name: 'Project',
      url: href + baseRouter + '/zhxc-project/',
      baseroute: '/project'
    },
    {
      name: 'MonitoringReport',
      url: href + baseRouter + '/zhxc-monitoringReport/',
      baseroute: '/monitoringReport'
    },
    {
      name: 'Monitoring',
      url: href + baseRouter + '/zhxc-monitoring/',
      baseroute: '/monitoring'
    },
    {
      name: 'Moniwarning',
      url: href + baseRouter + '/zhxc-moniwarning',
      baseroute: '/moniwarning'
    },
    {
      name: 'Shares',
      url: href + baseRouter + '/zhxc-shares/',
      baseroute: '/shares'
    },
    {
      name: 'Rtmws',
      url: href + baseRouter + '/zhxc-rtmws/',
      baseroute: '/rtmws'
    },
    {
      name: 'Village',
      url: href + baseRouter + '/zhxc-village/',
      baseroute: '/village'
    },
    {
      name: 'Organization',
      url: href + baseRouter + '/zhxc-organization/',
      baseroute: '/organization'
    },
    {
      name: 'Democratic',
      url: href + baseRouter + '/zhxc-democratic/',
      baseroute: '/democratic'
    },
    {
      name: 'Financial',
      url: href + baseRouter + '/zhxc-financial/',
      baseroute: '/financial',
      microAppChange: 'handleFinancialChange'
    },
    {
      name: 'Acvs',
      url: href + baseRouter + '/zhxc-acvs/',
      baseroute: '/acvs'
    },
    {
      name: 'Cms',
      url: href + baseRouter + '/zhxc-cms/',
      baseroute: '/cms'
    },
    {
      name: 'Moniwarning',
      url: href + baseRouter + '/zhxc-moniwarning/',
      baseroute: '/moniwarning'
    },
    {
      name: 'Njtj',
      url: href + baseRouter + '/zhxc-njtj/',
      baseroute: '/njtj'
    },
    // {
    //   name: 'Payment',
    //   url: href + baseRouter + '/zhxc-payment/',
    //   baseroute: '/payment'
    // },
    // {
    //   name: 'Approval',
    //   url: href + baseRouter + '/zhxc-approval/',
    //   baseroute: '/approval'
    // },
    // {
    //   name: 'LeaderView',
    //   url: href + baseRouter + '/zhxc-leaderView/',
    //   baseroute: '/leaderView'
    // },
    // {
    //   name: 'Pledge',
    //   url: href + baseRouter + '/zhxc-pledge/',
    //   baseroute: '/pledge'
    // },
    // {
    //   name: 'Propertyright',
    //   url: href + baseRouter + '/zhxc-propertyright/',
    //   baseroute: '/propertyright'
    // },
    // {
    //   name: 'Agricultural',
    //   url: href + baseRouter + '/zhxc-agricultural/',
    //   baseroute: '/agricultural'
    // },
    // {
    //   name: 'Beefcattle',
    //   url: href + baseRouter + '/zhxc-beefcattle/',
    //   baseroute: '/beefcattle'
    // },
    // {
    //   name: 'Bill',
    //   url: href + baseRouter + '/zhxc-bill/',
    //   baseroute: '/bill'
    // },
    // {
    //   name: 'Discussion',
    //   url: href + baseRouter + '/zhxc-discussion/',
    //   baseroute: '/discussion'
    // },
    // {
    //   name: 'Econproj',
    //   url: href + baseRouter + '/zhxc-econproj/',
    //   baseroute: '/econproj'
    // },
    // {
    //   name: 'Housesite',
    //   url: href + baseRouter + '/zhxc-housesite/',
    //   baseroute: '/housesite'
    // },
    // {
    //   name: 'Integral',
    //   url: href + baseRouter + '/zhxc-integral/',
    //   baseroute: '/integral'
    // },
    // {
    //   name: 'Landmgt',
    //   url: href + baseRouter + '/zhxc-landmgt/',
    //   baseroute: '/landmgt'
    // },
    // {
    //   name: 'PartyBuilding',
    //   url: href + baseRouter + '/zhxc-partyBuilding/',
    //   baseroute: '/partyBuilding'
    // },
    // {
    //   name: 'Peasantinfo',
    //   url: href + baseRouter + '/zhxc-peasantinfo/',
    //   baseroute: '/peasantinfo'
    // },
    // {
    //   name: 'Population',
    //   url: href + baseRouter + '/zhxc-population/',
    //   baseroute: '/population'
    // },
    // {
    //   name: 'Povertyrelief',
    //   url: href + baseRouter + '/zhxc-povertyrelief/',
    //   baseroute: '/povertyrelief'
    // },
    // {
    //   name: 'Supervise',
    //   url: href + baseRouter + '/zhxc-supervise/',
    //   baseroute: '/supervise'
    // },
    // {
    //   name: 'Vitalize',
    //   url: href + baseRouter + '/zhxc-vitalize/',
    //   baseroute: '/vitalize'
    // },
    // {
    //   name: 'Engineering',
    //   url: href + baseRouter + '/zhxc-engineering/',
    //   baseroute: '/engineering'
    // }
  ]
}
var conf_module = {
  RICH_EDITOR_FILE_MAX_SIZE: 300 // MB
}
const ytServiceUrl = 'http://192.168.5.189:16005/ytApi'
localStorage.setItem('ytServiceUrl', ytServiceUrl)
