/* ***********************   容器    *************************** */
div {
		display: block;
}

.printContainer {
		position: relative;
		margin: 0 auto;
		padding: 0 15px;
		box-sizing: border-box;
		height: 100%;
		overflow: hidden;
}

.printRow {
		margin-bottom: 10px;
}

.printRow:after,
.printRow:before {
		content: '';
		display: block;
		clear: both;
}

.printTitleBox {
		width: 100%;
		height: calc(70vh);
		margin: 0 auto;
}
.printInfoBox {
		width: 100%;
		margin: 0 auto;
		padding-top: 20px;
}
/* ******************** 方框 + 合同台账 ******************************  */
.printOuterBox {
		width: 48%;
		border: solid 1px black;
		height: inherit;
		vertical-align: middle;
		margin: 0 auto;
		padding: 8px 10px 10px 10px;
}
.printInnerBox {
		border: solid 1px black;
		vertical-align: middle;
		height: inherit;
		text-align: center;
}

.printInnerBoxScape {
		margin: 0 auto;
		vertical-align: middle;
		text-align: center;
		height: inherit;
}

.printInnerBoxText {
		color: #000000;
		font-size: 62px;
		margin: 0 auto;
		position: relative;
		left: 0;
		top: 40%;
		text-align: center;
		/* line-height: 160mm; */
}

/* *********************** 合同台账4项指标  **************************** */
.printInfoOuterBody {
		width: 48%;
		height: 20%;
		vertical-align: middle;
		margin: 0 auto;
		font-size: 10px;
		padding-top: 5mm
}


.printInfoInnerBody {
		padding: 1mm;
		width: 100%;
		text-align: center;
}

.printInfoItems {
		width: 100%;
		height: 10.3125mm
}

.printInfoItemTitle {
		float: left;
		width: 35%;
		text-align: left;
		font-size: 12px
}

.printInfoItemContext {
		float: left;
		width: 65%;
}

.printInfoItemContext_fit {
		border-bottom: solid 1px #000000;
		font-size: 12px;
		height: 14px;
		padding-bottom: 0
}
/* *************************  自适应宽度  ******************************* */
@media screen and (min-width: 1200px) {
		.printContainer {
				width: 1170px;
		}
}

@media screen and (min-width: 992px) {
		.printContainer {
				width: 970px;
		}
}

@media screen and (min-width: 768px) {
		.printContainer {
				width: 750px;
		}

}