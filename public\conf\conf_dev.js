var conf_dev = {
  microAppList: [
    // {
    //   name: 'System',
    //   url: 'http://localhost:10000',
    //   baseroute: '/system'
    // }
    {
      name: 'Assets',
      url: 'http://localhost:10003',
      baseroute: '/assets'
    },
    // {
    //   name: 'Njtj',
    //   url: 'http://localhost:10001',
    //   baseroute: '/njtj'
    // },
    // {
    //   name: 'Trading',
    //   url: 'http://*************:10008',
    //   baseroute: '/trading'
    // }
    {
      name: 'Contract',
      url: 'http://localhost:10003',
      baseroute: '/contract'
    },
    // {
    //   name: 'Approval',
    //   url: 'http://localhost:10004',
    //   baseroute: '/approval'
    // },
    {
      name: 'Acvs',
      url: 'http://localhost:10001',
      baseroute: '/acvs'
    },
    // {
    //   name: 'Pledge',
    //   url: 'http://localhost:10007',
    //   baseroute: '/pledge'
    // }
    {
      name: 'Financial',
      url: 'http://localhost:10008',
      baseroute: '/financial',
      microAppChange: 'handleFinancialChange'
    },
    {
      name: 'Shares',
      url: 'http://localhost:10001',
      baseroute: '/shares'
    }
    // {
    //   name: 'LeaderView',
    //   url: 'http://localhost:10009',
    //   baseroute: '/leaderView'
    // },
    // {
    //   name: 'Population',
    //   url: 'http://localhost:10010',
    //   baseroute: '/population'
    // }
    // {
    //   name: 'Propertyright',
    //   url: 'http://localhost:10011',
    //   baseroute: '/propertyright'
    // },
    // {
    //   name: 'Discussion',
    //   url: 'http://localhost:10012',
    //   baseroute: '/discussion'
    // },
    // {
    //   name: 'Bill',
    //   url: 'http://localhost:10009',
    //   baseroute: '/bill'
    // },
    // {
    //   name: 'Econproj',
    //   url: 'http://localhost:10014',
    //   baseroute: '/econproj'
    // },
    // {
    //   name: 'Beefcattle',
    //   url: 'http://localhost:10015',
    //   baseroute: '/beefcattle'
    // },
    // {
    //   name: 'PartyBuilding',
    //   url: 'http://localhost:10016',
    //   baseroute: '/partyBuilding'
    // },
    // {
    //   name: 'Agricultural',
    //   url: 'http://localhost:10018',
    //   baseroute: '/agricultural'
    // },
    // {
    //   name: 'Vitalize',
    //   url: 'http://localhost:10021',
    //   baseroute: '/vitalize'
    // },
    // {
    //   name: 'Povertyrelief',
    //   url: 'http://localhost:10022',
    //   baseroute: '/povertyrelief'
    // },
    // {
    //   name: 'Supervise',
    //   url: 'http://localhost:10023',
    //   baseroute: '/supervise'
    // },
    // {
    //   name: 'Village',
    //   url: 'http://localhost:10025',
    //   baseroute: '/village'
    // },
    // {
    //   name: 'Population',
    //   url: 'http://localhost:10010',
    //   baseroute: '/population'
    // },
    // {
    //   name: 'Peasantinfo',
    //   url: 'http://localhost:10011',
    //   baseroute: '/peasantinfo'
    // }
  ]
}
var conf_module = {
  RICH_EDITOR_FILE_MAX_SIZE: 300 // MB
}
// export const ytServiceUrl = 'http://*************:16005/ytApi'
